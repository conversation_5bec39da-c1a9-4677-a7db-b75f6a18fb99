#!/bin/bash

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 定义错误检查函数
check_result() {
    if [ $1 -ne 0 ]; then
        echo -e "${RED}$2${NC}"
        exit 1
    fi
}

# 获取当前路径
ANDROID_DIR=$(pwd)
LONGAN_DIR="${ANDROID_DIR}/../longan"
PACK4DIST=0

echo -e "${BLUE}ANDROID_DIR: ${ANDROID_DIR}${NC}"
echo -e "${BLUE}LONGAN_DIR: ${LONGAN_DIR}${NC}"

# 判断 longan 目录是否存在
if [ ! -d "${LONGAN_DIR}" ]; then
    echo -e "${RED}错误:longan 目录不存在:${LONGAN_DIR}${NC}"
    exit 1
fi

# 是否制作升级包
echo -e "${YELLOW}是否需要制作OTA升级包? [y/n]${NC}"
read -r answer
if [[ "$answer" == "y" || "$answer" == "Y" ]]; then
    PACK4DIST=1
fi

# 检查是否存在 old_target_files.zip
if [ $PACK4DIST -eq 1 ] && [ -f old_target_files.zip ]; then
    echo -e "${YELLOW}检测到 old_target_files.zip, 将用于制作差分包, 按回车继续...${NC}"
    read -r dummy
fi

# 编译 longan
cd "${LONGAN_DIR}" || exit 1
source build/envsetup.sh

if [ ! -f .buildconfig ]; then
    ./build.sh config
    check_result $? "LONGAN 配置失败"
fi

./build.sh
check_result $? "LONGAN 编译失败"
echo -e "${GREEN}LONGAN 编译成功${NC}"

# 编译 Android
cd "${ANDROID_DIR}" || exit 1
source build/envsetup.sh

lunch mercury_demo2-userdebug
check_result $? "lunch 失败"

extract-bsp
check_result $? "extract-bsp 失败"

JOBS=$(( $(nproc) > 8 ? 8 : $(nproc) ))
make -j${JOBS}
check_result $? "Android 编译失败"
echo -e "${GREEN}Android 编译成功${NC}"

# 打包
pack
check_result $? "Android 打包失败"
echo -e "${GREEN}Android 打包成功${NC}"

# 如果需要制作升级包
if [ $PACK4DIST -eq 1 ]; then
    pack4dist
    check_result $? "OTA 升级包制作失败"
    echo -e "${GREEN}OTA 升级包制作成功${NC}"
    echo -e "${YELLOW}注意保存[target files package]文件用于下次差分包制作${NC}"
fi
