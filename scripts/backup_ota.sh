#!/bin/bash

# 拷贝函数，带失败检查
copy_file_with_progress() {
    local SRC="$1"
    local DST_DIR="$2"
    local FILENAME=$(basename "$SRC")

    if [ ! -f "$SRC" ]; then
        echo "文件不存在：$SRC"
        exit 1
    fi

    echo "正在拷贝 $FILENAME..."
    rsync -ah --info=progress2 "$SRC" "$DST_DIR/" || { echo "拷贝失败：$SRC"; exit 1; }
}


# 获取传入目录或提示输入
if [ -n "$1" ]; then
    BACKUP_DIR="$1"
else
    read -p "请输入备份目录路径(例如 ../backup/bak3): " BACKUP_DIR
fi

# 检查目录是否为空
if [ -z "$BACKUP_DIR" ]; then
    echo "未输入目录，退出。"
    exit 1
fi

# 目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    echo "⚠️ 目录 $BACKUP_DIR 不存在。"
    read -p "是否创建该目录？[y/n]: " CREATE_DIR
    if [[ "$CREATE_DIR" =~ ^[Yy]$ ]]; then
        mkdir -p "$BACKUP_DIR" || { echo "创建目录失败"; exit 1; }
        echo "已创建目录 $BACKUP_DIR"
    else
        echo "用户取消操作，退出。"
        exit 1
    fi
fi

# 开始执行拷贝任务
copy_file_with_progress "../longan/out/t517_android10_demo2.0_uart0.img" "$BACKUP_DIR"
copy_file_with_progress "out/target/product/mercury-demo/obj/PACKAGING/target_files_intermediates/mercury_demo2-target_files-eng.tyw.zip" "$BACKUP_DIR"
copy_file_with_progress "out/target/product/mercury-demo/mercury_demo2-full_ota-eng.tyw.zip" "$BACKUP_DIR"
copy_file_with_progress "out/target/product/mercury-demo/mercury_demo2-inc_ota-eng.tyw.zip" "$BACKUP_DIR"

echo "所有文件拷贝完成，备份目录：$BACKUP_DIR"

# 询问是否需要软件接${BACKUP_DIR}/mercury_demo2-target_files-eng.tyw.zip 到当前目录 old_target_files.zip
# 判断源文件和目标文件是否存在
# 如果当前目录 old_target_files.zip 存在, 删除
# 将源文件连接到当前文件夹 old_target_files.zip

if [ -f "${BACKUP_DIR}/mercury_demo2-target_files-eng.tyw.zip" ]; then
    echo "源文件存在: ${BACKUP_DIR}/mercury_demo2-target_files-eng.tyw.zip"
    read -p "是否要创建软连接？(y/n): " choice
    if [ "$choice" = "y" ]; then
        if [ -f "old_target_files.zip" ]; then
            echo "检测到旧的软连接存在: old_target_files.zip, 是否更新软连接？(y/n): "
            read -p "选择(y/n): " choice
            if [ "$choice" = "y" ]; then                
                rm -f old_target_files.zip
                echo "正在更新软链接..."
                ln -s "${BACKUP_DIR}/mercury_demo2-target_files-eng.tyw.zip" "old_target_files.zip"
                echo "软连接更新成功.."
            else
                echo "取消更新软连接。"
            fi
        else 
            ln -s "${BACKUP_DIR}/mercury_demo2-target_files-eng.tyw.zip" "old_target_files.zip"
            echo "创建软连接成功"
        fi
    else
        echo "取消创建软连接。"
    fi
fi
