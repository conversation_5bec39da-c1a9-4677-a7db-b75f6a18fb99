#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 文件路径
FILE="device/softwinner/mercury-demo/device_common.mk"
KEY="ro.vendor.build.version.ota"

# 判断文件是否存在
if [ ! -f "$FILE" ]; then
    echo -e "${RED}错误：找不到文件：$FILE${NC}"
    exit 1
fi

# 提取字段值（假设每行结构是 ro.vendor.build.version.ota=1.01.100）
line=$(grep "$KEY=" "$FILE")
if [[ -z "$line" ]]; then
    echo -e "${RED}错误：找不到字段：$KEY=${NC}"
    exit 1
fi

# 提取版本号部分
full_version=$(echo "$line" | sed -n "s/.*$KEY=\([^ \\]*\).*/\1/p")
if [[ ! $full_version =~ ^[0-9]{1,2}\.[0-9]{1,2}\.[0-9]{3}$ ]]; then
    echo -e "${RED}错误：版本号格式非法, 应为 X.XX.YY, 例如 1.01.100${NC}"
    exit 1
fi

# 当前版本和上一版本
current_version=$(echo "$full_version" | cut -d '.' -f1-2)
previous_version=$(echo "$full_version" | cut -d '.' -f3)
# Add a dot after the first digit
previous_version=$(echo "$previous_version" | sed 's/\(^[0-9]\)/\1./')

echo -e "${YELLOW}当前版本：$current_version"
echo -e "上一版本：$previous_version${NC}"

# 用户输入新版本
while true; do
    read -p "请输入新版本号（格式必须为 X.XX, 例如 1.02）: " input_version

    if [[ ! $input_version =~ ^[0-9]{1,2}\.[0-9]{1,2}$ ]]; then
        echo -e "${RED}错误：格式应为 X.XX, 例如 1.03${NC}"
        continue
    fi

    input_float=$(echo "$input_version" | awk -F. '{printf "%.2f", $1 + $2/100}')
    current_float=$(echo "$current_version" | awk -F. '{printf "%.2f", $1 + $2/100}')

    if (( $(echo "$input_float <= $current_float" | bc -l) )); then
        echo -e "${RED}错误：新版本必须大于当前版本 $current_version${NC}"
        continue
    fi

    break
done

# 构造新字段值：新版本 + 当前版本去掉点（如 1.01 => 101）
prev_digits=$(echo "$current_version" | awk -F. '{printf "%d%02d", $1, $2}')
new_ota_version="${input_version}.${prev_digits}"

# 替换原字段
sed -i "s/\($KEY=\)[^ \\]*/\1$new_ota_version/" "$FILE"

# 校验
if grep -q "$KEY=$new_ota_version" "$FILE"; then
    echo -e "${GREEN}版本更新成功：$KEY=$new_ota_version${NC}"
else
    echo -e "${RED}替换失败, 请手动检查文件${NC}"
    exit 1
fi
