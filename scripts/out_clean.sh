#!/bin/bash

# 设置产品目录名（可用 ls out/target/product/ 查看）
PRODUCT_NAME=mercury-demo
PRODUCT_OUT=out/target/product/$PRODUCT_NAME

echo "========== 清理 Android 构建中间产物（保留可加速编译部分） =========="

# 1. 删除打包生成的 images/pac/xml/bin/lz4 等旧产物
echo "-> 删除旧的打包镜像文件"
rm -f $PRODUCT_OUT/*.img
rm -f $PRODUCT_OUT/*.pac
rm -f $PRODUCT_OUT/*.xml
rm -f $PRODUCT_OUT/*.bin
rm -f $PRODUCT_OUT/*.gz
rm -f $PRODUCT_OUT/*.lz4
rm -f $PRODUCT_OUT/*.mbn
rm -f $PRODUCT_OUT/*.ini

# 2. 删除分区目录（system/vendor/product 等打包目录内容）
echo "-> 删除 system/vendor/product 分区内容（保留 obj 编译结果）"
rm -rf $PRODUCT_OUT/system
rm -rf $PRODUCT_OUT/vendor
rm -rf $PRODUCT_OUT/product
rm -f $PRODUCT_OUT/installed-files*.txt

# 3. 删除打包临时目录
echo "-> 删除打包临时目录"
rm -rf $PRODUCT_OUT/pack_temp
rm -rf $PRODUCT_OUT/package-file*
rm -rf $PRODUCT_OUT/merged
rm -rf $PRODUCT_OUT/dump

# 4. 删除构建脚本缓存（无编译实质作用）
echo "-> 删除生成的 ninja/env 构建脚本"
rm -f out/build-*.ninja
rm -f out/combined-*.ninja
rm -f out/env-*.sh
rm -f out/ninja-*.sh

# 5. 删除日志和 trace（防止误判打包状态）
echo "-> 删除构建日志和 trace"
rm -f out/*.log
rm -f out/*.log.*.gz
rm -f out/soong.*.log
rm -f out/build.trace*
rm -f out/verbose.log*

# 6. 保留：
echo "✅ 已保留以下加速编译所需目录："
echo "   - $PRODUCT_OUT/obj"
echo "   - out/soong"
echo "   - out/host"
echo "   - out/target intermediates"

echo "✅ 清理完成，确保打包干净。"

